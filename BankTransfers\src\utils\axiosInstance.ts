import axios from 'axios';

const axiosInstance = axios.create({
  timeout: 10000,
  headers: {
    Accept: 'application/json',
    'Content-Type': 'application/json',
  },
});

axiosInstance.interceptors.response.use(
  response => response,
  error => {
    const status = error?.response?.status;
    const message = error?.response?.data?.message || error.message;
    console.error(`Axios error [${status || 'UNKNOWN'}]: ${message}`);
    return Promise.reject(error);
  }
);

export default axiosInstance;
