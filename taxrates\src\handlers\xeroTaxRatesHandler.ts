/**
 * Xero Tax Rates Data Synchronization Handler
 *
 * This AWS Lambda handler synchronizes Tax Rate and Tax Component data from Xero API.
 *
 * Key Features:
 * - Bulk insert and update of tax rates and their components
 * - Handles both API Gateway and SQS events
 * - Automatic Xero token refresh
 * - Comprehensive error handling and logging
 *
 * Usage:
 * - API Gateway: POST /taxrates/sync with { companyId: "uuid" }
 * - SQS: Queue message with { companyId: "uuid", tenantId?: "string" }
 *
 * Environment Variables Required:
 * - DATABASE_URL: PostgreSQL connection string
 * - XERO_CLIENT_ID: Xero OAuth client ID
 * - XERO_CLIENT_SECRET: Xero OAuth client secret
 * - XERO_REDIRECT_URI: Xero OAuth redirect URI
 * - XERO_BASE_URL: Xero API base URL (optional, defaults to production)
 */
import { APIGatewayProxyEvent, Context, APIGatewayProxyResult, SQSEvent } from 'aws-lambda';
import { PrismaClient, Company, Account } from '@prisma/client';

import axios from '../utils/axiosInstance';
import { refreshXeroToken } from '../services/refreshTokenService';
import { getXeroConfig } from '../config/environment';
import {
    XeroRequestData,
    ValidationError,
    XeroError,
} from '../types';

import dotenv from 'dotenv';
dotenv.config();

// Prisma
let prisma: PrismaClient | null = null;
function getPrismaClient(): PrismaClient {
    if (!prisma) {
        const config = {
            log: ['error', 'warn'] as Array<'error' | 'warn'>,
            errorFormat: 'pretty' as const,
        };
        if (process.env.IS_OFFLINE === 'true') {
            delete process.env.PRISMA_QUERY_ENGINE_LIBRARY;
        }
        prisma = new PrismaClient(config);
    }
    return prisma;
}

// Lambda handler
export const handler = async (
    event: APIGatewayProxyEvent | SQSEvent,
    context: Context
): Promise<APIGatewayProxyResult | void> => {
    if ("Records" in event && event.Records?.length) {
        // SQS
        for (const record of event.Records) {
            try {
                const requestData = JSON.parse(record.body);
                await processRequest(requestData, context);
            } catch (err) {
                console.error("Failed to process SQS message", err);
                throw err; // let Lambda/SQS retry
            }
        }
        return;
    } else {
        // API Gateway
        try {
            const requestData = parseRequestData(event);
            await processRequest(requestData, context);

            return {
                statusCode: 200,
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify({
                    success: true,
                    message: "Tax Rate data processed successfully",
                    timestamp: new Date().toISOString(),
                }),
            };
        } catch (err: any) {
            console.error("Handler error:", err);
            return {
                statusCode: err instanceof ValidationError ? 400 : 500,
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify({
                    success: false,
                    error: err.message,
                    type: err.constructor.name,
                }),
            };
        }
    }
};

// parse event
export function parseRequestData(
    event: APIGatewayProxyEvent | SQSEvent | { body?: string }
): XeroRequestData {
    // handle SQS
    if ("Records" in event && event.Records?.[0]) {
        try {
            const parsed = JSON.parse(event.Records[0].body);
            return parsed as XeroRequestData;
        } catch {
            throw new ValidationError("Failed to parse SQS message body");
        }
    }

    // handle API Gateway
    if ("body" in event && event.body) {
        try {
            const parsed =
                typeof event.body === "string" ? JSON.parse(event.body) : event.body;
            return parsed as XeroRequestData;
        } catch {
            throw new ValidationError("Failed to parse API Gateway body");
        }
    }

    throw new ValidationError("Invalid request data");
}

// process request
async function processRequest(
    requestData: XeroRequestData,
    _context: Context
): Promise<void> {
    try {
        validateRequestData(requestData);

        const prisma = getPrismaClient();
        const integration = await getActiveIntegration(requestData, prisma);

        if (!integration) {
            throw new Error("Active integration not found");
        }

        const validIntegration = await ensureValidToken(integration);

        console.log(`🚀 Fetching Tax Rates for company: ${requestData.companyId}`);

        // Fetch tax rates from Xero
        const taxRatesData = await getTaxRates(
            validIntegration.XeroAccessToken!,
            validIntegration.XeroTenantId!,
            requestData
        );

        if (taxRatesData && taxRatesData.length > 0) {
            if (requestData.dumpToDatabase) {
                await saveTaxRatesToDatabase(taxRatesData, requestData.companyId, prisma);
                console.log(`✅ Successfully processed ${taxRatesData.length} tax rates`);
            } else {
                console.log("Data fetched but not dumped to DB (dumpToDatabase is false)");
            }
        } else {
            console.log("No tax rates found to process");
        }

    } catch (error) {
        console.error("Error processing tax rates request:", error);
        throw error;
    }
}

// Validate request data
function validateRequestData(data: XeroRequestData): void {
    if (!data.companyId) {
        throw new ValidationError("companyId is required");
    }
}

// Get active integration
async function getActiveIntegration(data: XeroRequestData, prisma: PrismaClient): Promise<Company> {
    const integration = await prisma.company.findFirst({
        where: {
            Id: data.companyId,
            ConnectionStatus: "ACTIVE",
        },
    });

    if (!integration) {
        throw new Error("Active integration not found for company");
    }

    return integration;
}

// Ensure valid token
async function ensureValidToken(integration: Company): Promise<Company> {
    if (!integration.XeroAccessToken || !integration.XeroTokenExpiry) {
        throw new Error("No access token found");
    }

    const now = new Date();
    const expiry = new Date(integration.XeroTokenExpiry);
    // Add a 10 minute buffer (600,000 ms)
    if (expiry.getTime() - now.getTime() <= 10 * 60 * 1000) {
        console.log("Token expired or about to expire, refreshing...");
        return await refreshXeroToken(integration);
    }

    return integration;
}

// Get tax rates from Xero
const getTaxRates = async (
    accessToken: string,
    tenantId: string,
    requestData: XeroRequestData
): Promise<any[]> => {
    try {
        const config = getXeroConfig();
        const url = `${config.baseUrl}/TaxRates`;

        console.log(`Fetching tax rates from Xero API: ${url}`);

        const response = await axios.get(url, {
            headers: {
                'Authorization': `Bearer ${accessToken}`,
                'Xero-tenant-id': tenantId,
                'Accept': 'application/json'
            },
            params: requestData.where ? { where: requestData.where } : {},
        });

        // Log the full Xero response for debugging
        console.log('Xero TaxRates API raw response:', JSON.stringify(response.data, null, 2));

        if (response.data && response.data.TaxRates) {
            console.log(`Retrieved ${response.data.TaxRates.length} tax rates from Xero`);
            return response.data.TaxRates;
        }

        return [];
    } catch (error: any) {
        console.error("Error fetching tax rates from Xero:", error.response?.data || error.message);
        throw new XeroError(`Failed to fetch tax rates: ${error.response?.data?.Elements?.[0]?.ValidationErrors?.[0]?.Message || error.message}`);
    }
};

// Map a single Xero tax rate to Prisma TaxRate shape
function mapXeroTaxRateToPrisma(taxRate: any, companyId: string) {
    // Step 1: Map all direct fields from Xero to Prisma TaxRate model
    return {
        Name: taxRate.Name,
        TaxType: taxRate.TaxType,
        ReportTaxType: taxRate.ReportTaxType,
        CanApplyToAssets: taxRate.CanApplyToAssets,
        CanApplyToEquity: taxRate.CanApplyToEquity,
        CanApplyToExpenses: taxRate.CanApplyToExpenses,
        CanApplyToLiabilities: taxRate.CanApplyToLiabilities,
        CanApplyToRevenue: taxRate.CanApplyToRevenue,
        DisplayTaxRate: taxRate.DisplayTaxRate,
        EffectiveRate: taxRate.EffectiveRate,
        Status: taxRate.Status,
        UpdateUTCDate: new Date(new Date().toISOString()), // Use UTC date
        CompanyId: companyId
    };
}
// Map a single Xero tax component to Prisma TaxRateLine shape
function mapXeroTaxComponentToPrisma(taxRateName: string, component: any, companyId: string) {
    // Step 1: Map all direct fields from Xero to Prisma TaxRateLine model
    return {
        TaxRateName: taxRateName,
        TaxComponentsName: component.Name,
        Rate: component.Rate,
        IsCompound: component.IsCompound,
        IsNonRecoverable: component.IsNonRecoverable,
        CompanyId: companyId,
        IsRecentUpdatedInTable: true
    };
}
// Save tax rates and their components to database
async function saveTaxRatesToDatabase(
    taxRates: any[],
    companyId: string,
    prisma: PrismaClient
): Promise<void> {
    // Step 1: Map all Xero tax rates to Prisma TaxRate records
    console.log(`Saving ${taxRates.length} tax rates to database`);
    const taxRateRecords = taxRates.map(taxRate => mapXeroTaxRateToPrisma(taxRate, companyId));
    // Step 2: Fetch all existing TaxRates for this company
    const existingTaxRates = await prisma.taxRate.findMany({ where: { CompanyId: companyId } });
    const existingTaxRateNames = new Set(existingTaxRates.map(tr => tr.Name));
    // Step 3: Split into new and existing tax rates
    const taxRatesToInsert = taxRateRecords.filter(tr => !existingTaxRateNames.has(tr.Name));
    const taxRatesToUpdate = taxRateRecords.filter(tr => existingTaxRateNames.has(tr.Name));
    // Step 4: Bulk insert new tax rates
    if (taxRatesToInsert.length > 0) {
        await prisma.taxRate.createMany({ data: taxRatesToInsert, skipDuplicates: true });
        console.log(`Inserted ${taxRatesToInsert.length} new tax rates`);
    }
    // Step 5: Bulk update existing tax rates (use transaction)
    if (taxRatesToUpdate.length > 0) {
        await prisma.$transaction(
            taxRatesToUpdate.map(tr =>
                prisma.taxRate.updateMany({ where: { Name: tr.Name, CompanyId: companyId }, data: tr })
            )
        );
        console.log(`Updated ${taxRatesToUpdate.length} existing tax rates`);
    }
    // Step 6: Handle TaxRateLines (TaxComponents)
    let allComponentRecords: any[] = [];
    for (const taxRate of taxRates) {
        if (taxRate.TaxComponents && Array.isArray(taxRate.TaxComponents)) {
            for (const component of taxRate.TaxComponents) {
                allComponentRecords.push(mapXeroTaxComponentToPrisma(taxRate.Name, component, companyId));
            }
        }
    }
    // Fetch all existing TaxRateLines for these tax rates and company only (performance optimization)
    const fetchedTaxRateNamesArr = taxRates.map(tr => tr.Name);
    const existingLines = await prisma.taxRateLine.findMany({
        where: {
            CompanyId: companyId,
            TaxRateName: { in: fetchedTaxRateNamesArr }
        }
    });
    const existingLineKeys = new Set(existingLines.map(l => `${l.TaxRateName}||${l.TaxComponentsName}`));
    // Split into new and existing
    const linesToInsert = allComponentRecords.filter(l => !existingLineKeys.has(`${l.TaxRateName}||${l.TaxComponentsName}`));
    const linesToUpdate = allComponentRecords.filter(l => existingLineKeys.has(`${l.TaxRateName}||${l.TaxComponentsName}`));
    // Step 7: Delete extra TaxRateLines not present in latest Xero data for fetched tax rates only
    const fetchedTaxRateNames = new Set(taxRates.map(tr => tr.Name));
    const xeroLineKeys = new Set(allComponentRecords.map(l => `${l.TaxRateName}||${l.TaxComponentsName}`));
    const existingLinesForFetched = existingLines.filter(l => fetchedTaxRateNames.has(l.TaxRateName));
    const linesToDelete = existingLinesForFetched.filter(
        l => !xeroLineKeys.has(`${l.TaxRateName}||${l.TaxComponentsName}`)
    );
    if (linesToDelete.length > 0) {
        await prisma.taxRateLine.deleteMany({
            where: {
                CompanyId: companyId,
                OR: linesToDelete.map(l => ({
                    TaxRateName: l.TaxRateName,
                    TaxComponentsName: l.TaxComponentsName
                }))
            }
        });
        console.log(`Deleted ${linesToDelete.length} tax rate lines not present in latest Xero data for fetched tax rates.`);
    }
    // Step 8: Bulk insert new lines
    if (linesToInsert.length > 0) {
        await prisma.taxRateLine.createMany({ data: linesToInsert, skipDuplicates: true });
        console.log(`Inserted ${linesToInsert.length} new tax rate lines`);
    }
    // Step 9: Bulk update existing lines (use transaction)
    if (linesToUpdate.length > 0) {
        await prisma.$transaction(
            linesToUpdate.map(l =>
                prisma.taxRateLine.updateMany({ where: { TaxRateName: l.TaxRateName, TaxComponentsName: l.TaxComponentsName, CompanyId: companyId }, data: l })
            )
        );
        console.log(`Updated ${linesToUpdate.length} existing tax rate lines`);
    }
    console.log(`Inserted/Updated all tax rates and their components.`);
}

