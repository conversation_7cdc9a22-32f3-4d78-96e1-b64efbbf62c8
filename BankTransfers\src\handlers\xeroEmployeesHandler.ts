/**
 * Xero Employees Data Synchronization Handler
 *
 * This AWS Lambda handler synchronizes Employee data from Xero API.
 *
 * Key Features:
 * - Bulk insert and update of employees
 * - Handles both API Gateway and SQS events
 * - Automatic Xero token refresh
 * - Comprehensive error handling and logging
 * - Incremental sync support
 *
 * Usage:
 * - API Gateway: POST /employees/sync with { companyId: "uuid" }
 * - SQS: Queue message with { companyId: "uuid", tenantId?: "string" }
 *
 * Environment Variables Required:
 * - DATABASE_URL: PostgreSQL connection string
 * - XERO_CLIENT_ID: Xero OAuth client ID
 * - XERO_CLIENT_SECRET: Xero OAuth client secret
 * - XERO_BASE_URL: Xero API base URL (optional, defaults to production)
 */
import { APIGatewayProxyEvent, Context, APIGatewayProxyResult, SQSEvent } from 'aws-lambda';
import { PrismaClient, Company } from '@prisma/client';

import axios from '../utils/axiosInstance';
import { refreshXeroToken } from '../services/refreshTokenService';
import { getXeroConfig } from '../config/environment';
import { XeroRequestData, ValidationError, XeroError, ProcessedEmployeeData } from '../types';
import {
    startEmployeesExecution,
    completeEmployeesExecution,
    failEmployeesExecution,
    EmployeesExecutionResult,
} from '../utils/employeesLogger';
import {
    parseXeroDate,
    getCurrentSyncDate,
    generateXeroDateTime,
    formatDateForLogging,
} from '../utils/dateUtils';

// Production Configuration
const PRODUCTION_CONFIG = {
    API_TIMEOUT_MS: 30000,
    MAX_RETRIES: 3,
    RETRY_DELAY_MS: 1000,
};

// Get Prisma client
function getPrismaClient(): PrismaClient {
    return new PrismaClient({
        log: ['error', 'warn'],
        errorFormat: 'pretty',
    });
}

// Lambda handler
export const handler = async (
    event: APIGatewayProxyEvent | SQSEvent,
    context: Context
): Promise<APIGatewayProxyResult | void> => {
    if ('Records' in event && event.Records?.length) {
        // Handle SQS event - batch processing
        for (const record of event.Records) {
            try {
                const requestData = JSON.parse(record.body);
                await processRequest(requestData, context, 'SYSTEM');
            } catch (err) {
                console.error('❌ Failed to process SQS message', err);
                // Re-throw error to trigger SQS retry mechanism
                throw err;
            }
        }
        return;
    } else {
        // Handle API Gateway event - direct HTTP request
        try {
            const requestData = parseRequestData(event);
            await processRequest(requestData, context, 'USER');

            return {
                statusCode: 200,
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    success: true,
                    message: 'Employees data processed successfully',
                    timestamp: new Date().toISOString(),
                }),
            };
        } catch (err: any) {
            console.error('❌ Handler error:', err);
            return {
                statusCode: err instanceof ValidationError ? 400 : 500,
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    success: false,
                    error: err.message,
                    type: err.constructor.name,
                }),
            };
        }
    }
};

// parse event
export function parseRequestData(
    event: APIGatewayProxyEvent | SQSEvent | { body?: string }
): XeroRequestData {
    // handle SQS
    if ('Records' in event && event.Records?.[0]) {
        try {
            const parsed = JSON.parse(event.Records[0].body);
            return parsed as XeroRequestData;
        } catch {
            throw new ValidationError('Failed to parse SQS message body');
        }
    }

    // handle API Gateway
    if ('body' in event && event.body) {
        try {
            const parsed = typeof event.body === 'string' ? JSON.parse(event.body) : event.body;
            return parsed as XeroRequestData;
        } catch {
            throw new ValidationError('Failed to parse API Gateway body');
        }
    }

    throw new ValidationError('Invalid request data');
}

// process request
async function processRequest(
    requestData: XeroRequestData,
    _context: Context,
    triggeredBy: 'USER' | 'SYSTEM' = 'SYSTEM'
): Promise<void> {
    const startTime = Date.now();
    let integrationLogId: string | null = null;
    let recordsProcessed = 0;
    let recordsInserted = 0;
    let recordsUpdated = 0;

    try {
        // Start execution logging
        const { logId } = await startEmployeesExecution(requestData.companyId, triggeredBy);
        integrationLogId = logId;

        validateRequestData(requestData);

        const prisma = getPrismaClient();
        const integration = await getActiveIntegration(requestData, prisma);

        if (!integration) {
            throw new Error('Active integration not found');
        }

        const validIntegration = await ensureValidToken(integration);

        // Get last sync date for incremental sync
        const lastSyncDate = await getLastSyncDate(requestData.companyId, prisma);
        const syncStartTime = getCurrentSyncDate();

        console.log(`🚀 Fetching Employees for company: ${requestData.companyId}`, lastSyncDate);

        // Create request data with UpdatedDateUTC filter for incremental sync
        if (lastSyncDate != null) {
            requestData = {
                ...requestData,
                where: `UpdatedDateUTC>=${generateXeroDateTime(lastSyncDate)}`
            };
        }

        // Fetch employees from Xero with incremental sync
        const employeesData = await getBankTransfers(
            validIntegration.XeroAccessToken!,
            validIntegration.XeroTenantId!,
            requestData
        );

        if (employeesData && employeesData.length > 0) {
            if (requestData.dumpToDatabase !== false) {
                const saveResult = await saveBankTransferToDatabase(
                    employeesData,
                    requestData.companyId,
                    prisma
                );
                recordsProcessed = employeesData.length;
                recordsInserted = saveResult.inserted;
                recordsUpdated = saveResult.updated;

                console.log(
                    `✅ Successfully processed ${recordsProcessed} Bank Transfers (${recordsInserted} inserted, ${recordsUpdated} updated)`
                );
            } else {
                console.log('📊 Data fetched but not dumped to DB (dumpToDatabase is false)');
                recordsProcessed = employeesData.length;
            }
        } else {
            console.log('📭 No employees found to process');
        }

        // Update sync timestamp
        if (requestData.dumpToDatabase !== false) {
            await updateLastSyncDate(requestData.companyId, syncStartTime, prisma);
        }

        // Complete execution logging
        if (integrationLogId) {
            const result: EmployeesExecutionResult = {
                totalEmployees: recordsProcessed,
                processedEmployees: recordsProcessed,
                insertedEmployees: recordsInserted,
                updatedEmployees: recordsUpdated,
                errors: 0,
                warnings: 0,
                duration: `${Date.now() - startTime}ms`,
            };
            await completeEmployeesExecution(integrationLogId, startTime, result);
        }

    } catch (error: any) {
        console.error('❌ Process request error:', error);

        // Log execution failure
        if (integrationLogId) {
            const partialResult: Partial<EmployeesExecutionResult> = {
                totalEmployees: recordsProcessed,
                processedEmployees: recordsProcessed,
                insertedEmployees: recordsInserted,
                updatedEmployees: recordsUpdated,
                errors: 1,
                warnings: 0,
            };
            await failEmployeesExecution(integrationLogId, startTime, error, partialResult);
        }

        throw error;
    }
}

// Validate request data
function validateRequestData(data: XeroRequestData): void {
    if (!data.companyId) {
        throw new ValidationError('companyId is required');
    }
}

// Get active integration
async function getActiveIntegration(data: XeroRequestData, prisma: PrismaClient): Promise<Company> {
    const integration = await prisma.company.findFirst({
        where: {
            Id: data.companyId,
        },
    });

    if (!integration) {
        throw new Error('Active integration not found for company');
    }

    return integration;
}

// Ensure valid token
async function ensureValidToken(integration: Company): Promise<Company> {
    if (!integration.XeroAccessToken) {
        throw new Error('No Xero access token found');
    }

    // Check if token is expired (with 5-minute buffer)
    const now = new Date();
    const tokenExpiry = integration.XeroTokenExpiry;
    const bufferTime = 5 * 60 * 1000; // 5 minutes in milliseconds

    if (!tokenExpiry || now.getTime() > (tokenExpiry.getTime() - bufferTime)) {
        console.log('🔄 Token expired or expiring soon, refreshing...');
        return await refreshXeroToken(integration);
    }

    return integration;
}

// Get last sync date for incremental sync
async function getLastSyncDate(companyId: string, prisma: PrismaClient): Promise<Date | null> {
    try {
        const syncRecord = await prisma.xeroModuleSync.findUnique({
            where: {
                CompanyId_ModuleName: {
                    CompanyId: companyId,
                    ModuleName: 'Employees',
                },
            },
        });

        return syncRecord?.LastSyncTime || null;
    } catch (error) {
        console.error('❌ Error getting last sync date:', error);
        return null;
    }
}

// Update last sync date

async function updateLastSyncDate(companyId: string, syncDate: Date, prisma: PrismaClient): Promise<void> {
    try {
        await prisma.xeroModuleSync.upsert({
            where: {
                CompanyId_ModuleName: {
                    CompanyId: companyId,
                    ModuleName: 'Bank Transfers'
                }
            },
            update: {
                LastSyncTime: syncDate,
                UpdatedAt: getCurrentSyncDate()
            },
            create: {
                Id: require('uuid').v4(),
                CompanyId: companyId,
                ModuleName: 'Bank Transfers',
                LastSyncTime: syncDate,
                CreatedAt: getCurrentSyncDate(),
                UpdatedAt: getCurrentSyncDate()
            }
        });

        console.log(`📅 Updated last sync date to: ${formatDateForLogging(syncDate)}`);
    } catch (error) {
        console.error("Error updating last sync date:", error);
        // Don't throw here as this shouldn't fail the main sync process
    }
}
// Get employees from Xero with improved error handling and timeout configuration
const getBankTransfers = async (
    accessToken: string,
    tenantId: string,
    requestData: XeroRequestData
): Promise<any[]> => {
    const startTime = Date.now();

    try {
        const config = getXeroConfig();
        const url = `${config.baseUrl}Employees`;

        console.log(`🌐 API Request URL: ${url.replace(accessToken, '[REDACTED]')}`, requestData);
        console.log('Request Data:', tenantId);

        const response = await axios.get(url, {
            headers: {
                Authorization: `Bearer ${accessToken}`,
                'Xero-tenant-id': tenantId,
                Accept: 'application/json',
                'User-Agent': 'EmployeesSync/1.0.0',
            },
            params: requestData.where ? { where: requestData.where } : {},
            timeout: PRODUCTION_CONFIG.API_TIMEOUT_MS, // Configurable API timeout
        });

        const requestTime = Date.now() - startTime;
        console.log(`✅ Employees API call completed in ${requestTime}ms`);

        const employeesData = response?.data;

        if (!employeesData || !employeesData.Employees) {
            throw new Error('Invalid Employees data structure received from Xero');
        }

        console.log(`📊 Retrieved ${employeesData.Employees.length} employees from Xero`);
        return employeesData.Employees;
    } catch (error: any) {
        console.error(error);
        const requestTime = Date.now() - startTime;
        console.error(`❌ Employees API call failed after ${requestTime}ms:`, {
            error: error.message,
            status: error.response?.status,
            statusText: error.response?.statusText,
            tenantId: tenantId.substring(0, 8) + '...',
        });

        // Handle specific Xero API errors
        if (error.response?.status === 429) {
            throw new XeroError(`Rate limit exceeded. Please try again later.`, 429, error);
        }

        if (error.response?.status === 401) {
            throw new XeroError(`Authentication failed. Token may be expired.`, 401, error);
        }

        if (error.response?.status === 403) {
            throw new XeroError(`Access forbidden. Check your Xero permissions.`, 403, error);
        }

        if (error.response?.status === 404) {
            throw new XeroError(`Employees endpoint not found.`, 404, error);
        }

        if (error.code === 'ECONNABORTED') {
            throw new XeroError(`Request timeout after ${PRODUCTION_CONFIG.API_TIMEOUT_MS}ms`, 408, error);
        }

        // Generic error
        throw new XeroError(
            `Failed to fetch employees: ${error.message}`,
            error.response?.status || 500,
            error
        );
    }
};

// Save employees to database
async function saveBankTransferToDatabase(
    employees: any[],
    companyId: string,
    prisma: PrismaClient
): Promise<{ inserted: number; updated: number }> {
    console.log(`Saving ${employees.length} employees to database`);

    // 1. Map all employees to DB shape
    const employeeRecords: ProcessedEmployeeData[] = employees.map(employee => ({
        EmployeeID: employee.EmployeeID,
        Status: employee.Status || null,
        FirstName: employee.FirstName || null,
        LastName: employee.LastName || null,
        ExternalLinkUrl: employee.ExternalLink?.Url || null,
        ExternalLinkDescription: employee.ExternalLink?.Description || null,
        UpdateUTCDate: parseXeroDate(employee.UpdatedDateUTC) || new Date(),
        CompanyId: companyId,
    }));
    const employeeIds = employeeRecords.map(e => e.EmployeeID);

    // 2. Fetch existing EmployeeIDs
    const existingEmployees = await prisma.employee.findMany({
        where: { EmployeeID: { in: employeeIds } },
        select: { EmployeeID: true },
    });
    const existingEmployeeIds = new Set(existingEmployees.map(e => e.EmployeeID));

    // 3. Split into new and existing
    const employeesToInsert = employeeRecords.filter(e => !existingEmployeeIds.has(e.EmployeeID));
    const employeesToUpdate = employeeRecords.filter(e => existingEmployeeIds.has(e.EmployeeID));

    // 4. Bulk insert new employees
    if (employeesToInsert.length > 0) {
        await prisma.employee.createMany({ data: employeesToInsert, skipDuplicates: true });
        console.log(`📥 Inserted ${employeesToInsert.length} new employees`);
    }

    // 5. Bulk update existing employees
    if (employeesToUpdate.length > 0) {
        await prisma.$transaction(
            employeesToUpdate.map(employee =>
                prisma.employee.update({
                    where: { EmployeeID: employee.EmployeeID },
                    data: employee,
                })
            )
        );
        console.log(`🔄 Updated ${employeesToUpdate.length} existing employees`);
    }

    return {
        inserted: employeesToInsert.length,
        updated: employeesToUpdate.length,
    };
}
