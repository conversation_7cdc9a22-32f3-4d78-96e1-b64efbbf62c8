/**
 * Employees Logger Utility
 *
 * This utility provides simplified logging functionality specifically for
 * Employees Lambda function execution using only the IntegrationLog model.
 * It follows the same pattern as Accounts logging.
 *
 * Key Features:
 * - Lambda function execution logging (success/failure)
 * - Integration with existing IntegrationLog model
 * - Functional implementation
 * - Comprehensive error handling
 *
 * <AUTHOR> Logger Utility
 * @version 1.0.0
 */

import {
  logLambdaInvocation,
  updateLambdaSuccess,
  updateLambdaError,
  formatDuration,
} from './integrationLogger';

/**
 * Employees Execution Result Interface
 */
export interface EmployeesExecutionResult {
  totalEmployees: number;
  processedEmployees: number;
  insertedEmployees: number;
  updatedEmployees: number;
  errors: number;
  warnings: number;
  duration: string;
}

/**
 * Start Employees Lambda execution logging
 */
export async function startEmployeesExecution(
  companyId: string,
  triggeredBy: 'USER' | 'SYSTEM' = 'SYSTEM'
): Promise<{ logId: string; startTime: number }> {
  try {
    const startTime = Date.now();
    const logId = await logLambdaInvocation(companyId, 'Employees', triggeredBy);

    return { logId, startTime };
  } catch (error) {
    console.error('❌ Failed to start Employees execution logging:', error);
    throw error;
  }
}

/**
 * Complete Employees Lambda execution with success
 */
export async function completeEmployeesExecution(
  logId: string,
  startTime: number,
  result: EmployeesExecutionResult
): Promise<void> {
  try {
    const executionTime = (Date.now() - startTime).toString();

    const summary = {
      totalEmployees: result.totalEmployees,
      processedEmployees: result.processedEmployees,
      insertedEmployees: result.insertedEmployees,
      updatedEmployees: result.updatedEmployees,
      errors: result.errors,
      warnings: result.warnings,
      executionTime: formatDuration(executionTime),
      successRate: result.totalEmployees > 0 ?
        ((result.processedEmployees - result.errors) / result.totalEmployees * 100).toFixed(2) + '%' : '0%',
    };

    await updateLambdaSuccess(logId, executionTime, "Employees Sync successful", summary);

    console.log('📊 Employees Execution Summary:', {
      ...summary,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('❌ Failed to complete Employees execution logging:', error);
    // Don't throw here to avoid masking the original success
  }
}

/**
 * Complete Employees Lambda execution with error
 */
export async function failEmployeesExecution(
  logId: string,
  startTime: number,
  error: any,
  partialResult?: Partial<EmployeesExecutionResult>
): Promise<void> {
  try {
    const executionTime = (Date.now() - startTime).toString();

    const summary = {
      totalEmployees: partialResult?.totalEmployees || 0,
      processedEmployees: partialResult?.processedEmployees || 0,
      insertedEmployees: partialResult?.insertedEmployees || 0,
      updatedEmployees: partialResult?.updatedEmployees || 0,
      errors: (partialResult?.errors || 0) + 1, // +1 for the current error
      warnings: partialResult?.warnings || 0,
      executionTime: formatDuration(executionTime),
      errorType: error.constructor.name,
      errorMessage: error.message,
    };

    await updateLambdaError(logId, executionTime, error, summary);

    console.error('💥 Employees Execution Failed:', {
      ...summary,
      timestamp: new Date().toISOString(),
    });
  } catch (logError) {
    console.error('❌ Failed to log Employees execution error:', logError);
    // Don't throw here to avoid masking the original error
  }
}
