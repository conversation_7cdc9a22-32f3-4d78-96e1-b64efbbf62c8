/**
 * Xero Items Data Synchronization Handler
 *
 * This AWS Lambda handler synchronizes Items from Xero API.
 *
 * Key Features:
 * - Bulk insert and update of items
 * - Handles both API Gateway and SQS events
 * - Automatic Xero token refresh
 * - Comprehensive error handling and logging
 *
 * Usage:
 * - API Gateway: POST /items/sync with { companyId: "uuid" }
 * - SQS: Queue message with { companyId: "uuid", tenantId?: "string" }
 *
 * Environment Variables Required:
 * - DATABASE_URL: PostgreSQL connection string
 * - XERO_CLIENT_ID: Xero OAuth client ID
 * - XERO_CLIENT_SECRET: Xero OAuth client secret
 * - XERO_REDIRECT_URI: Xero OAuth redirect URI
 * - XERO_BASE_URL: Xero API base URL (optional, defaults to production)
 */
import { APIGatewayProxyEvent, Context, APIGatewayProxyResult, SQSEvent } from 'aws-lambda';
import { PrismaClient, Company, Account } from '@prisma/client';

import axios from '../utils/axiosInstance';
import { refreshXeroToken } from '../services/refreshTokenService';
import { getXeroConfig } from '../config/environment';
import {
    XeroRequestData,
    ValidationError,
    XeroError,
} from '../types';

import dotenv from 'dotenv';
dotenv.config();

// Prisma
let prisma: PrismaClient | null = null;
function getPrismaClient(): PrismaClient {
    if (!prisma) {
        const config = {
            log: ['error', 'warn'] as Array<'error' | 'warn'>,
            errorFormat: 'pretty' as const,
        };
        if (process.env.IS_OFFLINE === 'true') {
            delete process.env.PRISMA_QUERY_ENGINE_LIBRARY;
        }
        prisma = new PrismaClient(config);
    }
    return prisma;
}

// Lambda handler
export const handler = async (
    event: APIGatewayProxyEvent | SQSEvent,
    context: Context
): Promise<APIGatewayProxyResult | void> => {
    if ("Records" in event && event.Records?.length) {
        // SQS
        for (const record of event.Records) {
            try {
                const requestData = JSON.parse(record.body);
                await processRequest(requestData, context);
            } catch (err) {
                console.error("Failed to process SQS message", err);
                throw err; // let Lambda/SQS retry
            }
        }
        return;
    } else {
        // API Gateway
        try {
            const requestData = parseRequestData(event);
            await processRequest(requestData, context);

            return {
                statusCode: 200,
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify({
                    success: true,
                    message: "Item data processed successfully",
                    timestamp: new Date().toISOString(),
                }),
            };
        } catch (err: any) {
            console.error("Handler error:", err);
            return {
                statusCode: err instanceof ValidationError ? 400 : 500,
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify({
                    success: false,
                    error: err.message,
                    type: err.constructor.name,
                }),
            };
        }
    }
};

// parse event
export function parseRequestData(
    event: APIGatewayProxyEvent | SQSEvent | { body?: string }
): XeroRequestData {
    // handle SQS
    if ("Records" in event && event.Records?.[0]) {
        try {
            const parsed = JSON.parse(event.Records[0].body);
            return parsed as XeroRequestData;
        } catch {
            throw new ValidationError("Failed to parse SQS message body");
        }
    }

    // handle API Gateway
    if ("body" in event && event.body) {
        try {
            const parsed =
                typeof event.body === "string" ? JSON.parse(event.body) : event.body;
            return parsed as XeroRequestData;
        } catch {
            throw new ValidationError("Failed to parse API Gateway body");
        }
    }

    throw new ValidationError("Invalid request data");
}

// process request
async function processRequest(
    requestData: XeroRequestData,
    _context: Context
): Promise<void> {
    try {
        validateRequestData(requestData);
        const prisma = getPrismaClient();
        const integration = await getActiveIntegration(requestData, prisma);
        if (!integration) {
            throw new Error("Active integration not found");
        }
        const validIntegration = await ensureValidToken(integration);
        console.log(`🚀 Fetching Items for company: ${requestData.companyId}`);
        // Fetch items from Xero
        const itemsData = await getItems(
            validIntegration.XeroAccessToken!,
            validIntegration.XeroTenantId!,
            requestData
        );
        if (itemsData && itemsData.length > 0) {
            if (requestData.dumpToDatabase) {
                await saveItemsToDatabase(itemsData, requestData.companyId, prisma);
                console.log(`✅ Successfully processed ${itemsData.length} items`);
            } else {
                console.log("Data fetched but not dumped to DB (dumpToDatabase is false)");
            }
        } else {
            console.log("No items found to process");
        }
    } catch (error) {
        console.error("Error processing items request:", error);
        throw error;
    }
}

// Validate request data
function validateRequestData(data: XeroRequestData): void {
    if (!data.companyId) {
        throw new ValidationError("companyId is required");
    }
}

// Get active integration
async function getActiveIntegration(data: XeroRequestData, prisma: PrismaClient): Promise<Company> {
    const integration = await prisma.company.findFirst({
        where: {
            Id: data.companyId,
            ConnectionStatus: "ACTIVE",
        },
    });

    if (!integration) {
        throw new Error("Active integration not found for company");
    }

    return integration;
}

// Ensure valid token
async function ensureValidToken(integration: Company): Promise<Company> {
    if (!integration.XeroAccessToken || !integration.XeroTokenExpiry) {
        throw new Error("No access token found");
    }

    const now = new Date();
    const expiry = new Date(integration.XeroTokenExpiry);
    // Add a 10 minute buffer (600,000 ms)
    if (expiry.getTime() - now.getTime() <= 10 * 60 * 1000) {
        console.log("Token expired or about to expire, refreshing...");
        return await refreshXeroToken(integration);
    }

    return integration;
}

// Get items from Xero
const getItems = async (
    accessToken: string,
    tenantId: string,
    requestData: XeroRequestData
): Promise<any[]> => {
    try {
        const config = getXeroConfig();
        const url = `${config.baseUrl}/Items`;
        console.log(`Fetching items from Xero API: ${url}`);
        const response = await axios.get(url, {
            headers: {
                'Authorization': `Bearer ${accessToken}`,
                'Xero-tenant-id': tenantId,
                'Accept': 'application/json'
            },
            params: requestData.where ? { where: requestData.where } : {},
        });
        // Log the full Xero response for debugging
        console.log('Xero Items API raw response:', JSON.stringify(response.data, null, 2));
        if (response.data && response.data.Items) {
            console.log(`Retrieved ${response.data.Items.length} items from Xero`);
            return response.data.Items;
        }
        return [];
    } catch (error: any) {
        console.error("Error fetching items from Xero:", error.response?.data || error.message);
        throw new XeroError(`Failed to fetch items: ${error.response?.data?.Elements?.[0]?.ValidationErrors?.[0]?.Message || error.message}`);
    }
};

// Map a single Xero item to Prisma Item shape
function mapXeroItemToPrisma(item: any, companyId: string) {
    // Step 1: Map all direct fields from Xero to Prisma Item model
    return {
        ItemID: item.ItemID,
        Name: item.Name,
        Code: item.Code,
        Description: item.Description,
        PurchaseDescription: item.PurchaseDescription,
        IsPurchased: item.IsPurchased,
        IsSold: item.IsSold,
        IsTrackedAsInventory: item.IsTrackedAsInventory,
        TotalCostPool: item.TotalCostPool ?? null,
        QuantityOnHand: item.QuantityOnHand ?? null,
        InventoryAssetAccountCode: item.InventoryAssetAccountCode ?? null,
        PurchaseAccountCode: item.PurchaseDetails?.AccountCode ?? null,
        PurchaseTaxType: item.PurchaseDetails?.TaxType ?? null,
        PurchaseUnitPrice: item.PurchaseDetails?.UnitPrice ?? null,
        COGSAccountCode: item.PurchaseDetails?.COGSAccountCode ?? null,
        SalesTaxType: item.SalesDetails?.TaxType ?? null,
        SalesAccountCode: item.SalesDetails?.AccountCode ?? null,
        SalesUnitPrice: item.SalesDetails?.UnitPrice ?? null,
        UpdateUTCDate: item.UpdatedDateUTC ? parseXeroDate(item.UpdatedDateUTC) : new Date(new Date().toISOString()),
        CompanyId: companyId
    };
}
// Save items to database
async function saveItemsToDatabase(
    items: any[],
    companyId: string,
    prisma: PrismaClient
): Promise<void> {
    // Step 1: Map all Xero items to Prisma Item records
    console.log(`Saving ${items.length} items to database`);
    const itemRecords = items.map(item => mapXeroItemToPrisma(item, companyId));
    const itemIds = itemRecords.map(item => item.ItemID);
    // Step 2: Fetch all existing Items for this company
    const existingItems = await prisma.item.findMany({ where: { CompanyId: companyId } });
    const existingItemIds = new Set(existingItems.map(i => i.ItemID));
    // Step 3: Split into new and existing items
    const itemsToInsert = itemRecords.filter(i => !existingItemIds.has(i.ItemID));
    const itemsToUpdate = itemRecords.filter(i => existingItemIds.has(i.ItemID));
    // Step 4: Bulk insert new items
    if (itemsToInsert.length > 0) {
        await prisma.item.createMany({ data: itemsToInsert, skipDuplicates: true });
        console.log(`Inserted ${itemsToInsert.length} new items`);
    }
    // Step 5: Bulk update existing items (use transaction)
    if (itemsToUpdate.length > 0) {
        await prisma.$transaction(
            itemsToUpdate.map(i =>
                prisma.item.updateMany({ where: { ItemID: i.ItemID, CompanyId: companyId }, data: i })
            )
        );
        console.log(`Updated ${itemsToUpdate.length} existing items`);
    }
    console.log(`Inserted/Updated all items.`);
}
// Helper function to parse Xero's .NET date format
function parseXeroDate(dateString: string | null | undefined): Date | null {
    if (!dateString || typeof dateString !== 'string') return null;
    const match = dateString.match(/\/Date\((\d+)([+-]\d{4})\)\//);
    if (match) {
        const timestamp = parseInt(match[1] || "0");
        return new Date(timestamp);
    }
    const parsedDate = new Date(dateString);
    return isNaN(parsedDate.getTime()) ? null : parsedDate;
}

